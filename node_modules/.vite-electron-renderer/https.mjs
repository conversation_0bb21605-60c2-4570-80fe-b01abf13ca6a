const avoid_parse_require = require; const _M_ = avoid_parse_require("https");
export const Agent = _M_.Agent;
export const globalAgent = _M_.globalAgent;
export const Server = _M_.Server;
export const createServer = _M_.createServer;
const keyword_get = _M_.get;
export const request = _M_.request;
const keyword_default = _M_.default || _M_;
export {
  keyword_get as get,
  keyword_default as default,
};