const avoid_parse_require = require; const _M_ = avoid_parse_require("http");
export const _connectionListener = _M_._connectionListener;
export const METHODS = _M_.METHODS;
export const STATUS_CODES = _M_.STATUS_CODES;
export const Agent = _M_.Agent;
export const ClientRequest = _M_.ClientRequest;
export const IncomingMessage = _M_.IncomingMessage;
export const OutgoingMessage = _M_.OutgoingMessage;
export const Server = _M_.Server;
export const ServerResponse = _M_.ServerResponse;
export const createServer = _M_.createServer;
export const validateHeaderName = _M_.validateHeaderName;
export const validateHeaderValue = _M_.validateHeaderValue;
const keyword_get = _M_.get;
export const request = _M_.request;
export const maxHeaderSize = _M_.maxHeaderSize;
export const globalAgent = _M_.globalAgent;
const keyword_default = _M_.default || _M_;
export {
  keyword_get as get,
  keyword_default as default,
};