const avoid_parse_require = require; const _M_ = avoid_parse_require("querystring");
export const unescapeBuffer = _M_.unescapeBuffer;
export const unescape = _M_.unescape;
export const escape = _M_.escape;
export const stringify = _M_.stringify;
export const encode = _M_.encode;
export const parse = _M_.parse;
export const decode = _M_.decode;
const keyword_default = _M_.default || _M_;
export {
  keyword_default as default,
};