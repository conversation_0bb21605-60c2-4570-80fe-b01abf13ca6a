/**
 * Figma API 类型定义
 * 统一管理所有 Figma 相关的 TypeScript 类型
 */

// 基础颜色类型
export interface FigmaColor {
  r: number;
  g: number;
  b: number;
  a: number;
}

// 填充类型
export interface FigmaFill {
  blendMode: string;
  type: string;
  color: FigmaColor;
}

// 边界框类型
export interface FigmaBoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 约束类型
export interface FigmaConstraints {
  vertical: string;
  horizontal: string;
}

// 导出设置类型
export interface FigmaExportSetting {
  suffix: string;
  format: string;
  constraint: {
    type: string;
    value: number;
  };
}

// Figma 节点类型定义（递归结构）
export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  scrollBehavior?: string;
  children?: FigmaNode[]; // 递归引用自身，支持嵌套结构
  blendMode?: string;
  clipsContent?: boolean;
  background?: FigmaFill[];
  fills?: FigmaFill[];
  strokes?: any[]; // 空数组用any代替
  strokeWeight?: number;
  strokeAlign?: string;
  backgroundColor?: FigmaColor;
  absoluteBoundingBox?: FigmaBoundingBox;
  absoluteRenderBounds?: FigmaBoundingBox;
  constraints?: FigmaConstraints;
  exportSettings?: FigmaExportSetting[];
  effects?: any[]; // 空数组用any代替
  interactions?: any[]; // 空数组用any代替
  visible?: boolean;
  locked?: boolean;
  preserveRatio?: boolean;
  layoutAlign?: string;
  layoutGrow?: number;
  layoutSizingHorizontal?: string;
  layoutSizingVertical?: string;
  transitionNodeID?: string;
  transitionDuration?: number;
  transitionEasing?: string;
  opacity?: number;
  size?: {
    x: number;
    y: number;
  };
  relativeTransform?: number[][];
  isMask?: boolean;
  fillGeometry?: any[];
  strokeGeometry?: any[];
  cornerRadius?: number;
  cornerSmoothing?: number;
  characters?: string;
  style?: any; // 空对象用any代替
  layoutVersion?: number;
  characterStyleOverrides?: any[];
  styleOverrideTable?: any; // 空对象用any代替
  lineTypes?: string[];
  lineIndentations?: number[];
}

// 节点信息包装类型
export interface FigmaNodeInfo {
  document: FigmaNode;
  components: any; // 空对象用any代替
  componentSets: any; // 空对象用any代替
  schemaVersion: number;
  styles: any; // 空对象用any代替
}

// Figma API 响应类型定义
export interface FigmaNodesResponse {
  name: string;
  lastModified: string;
  thumbnailUrl: string;
  version: string;
  role: string;
  editorType: string;
  linkAccess: string;
  nodes: {
    [nodeId: string]: FigmaNodeInfo;
  };
  err?: string;
}

// Figma 链接解析结果
export interface FigmaLinkInfo {
  fileKey: string;
  nodeId?: string;
  originalUrl: string;
}

// IPC 通信相关类型
export interface IpcRenderer {
  invoke(channel: string, ...args: any[]): Promise<any>;
}

// 全局窗口对象扩展
declare global {
  interface Window {
    ipcRenderer: IpcRenderer;
  }
}
