"use strict";
const electron = require("electron");
const node_module = require("node:module");
const node_url = require("node:url");
const path = require("node:path");
const https = require("https");
const http = require("http");
const querystring = require("querystring");
var _documentCurrentScript = typeof document !== "undefined" ? document.currentScript : null;
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const https__namespace = /* @__PURE__ */ _interopNamespaceDefault(https);
const http__namespace = /* @__PURE__ */ _interopNamespaceDefault(http);
const querystring__namespace = /* @__PURE__ */ _interopNamespaceDefault(querystring);
function makeHttpRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      const parsedUrl = new URL(url);
      const httpModule = parsedUrl.protocol === "https:" ? https__namespace : http__namespace;
      if (options.params) {
        const queryString = querystring__namespace.stringify(options.params);
        if (queryString) {
          parsedUrl.search = parsedUrl.search ? `${parsedUrl.search}&${queryString}` : `?${queryString}`;
        }
      }
      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (parsedUrl.protocol === "https:" ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || "GET",
        headers: {
          "Content-Type": "application/json",
          "User-Agent": "Electron-App/1.0.0",
          ...options.headers
        },
        timeout: options.timeout || 1e4
      };
      const req = httpModule.request(requestOptions, (res) => {
        let responseData = "";
        res.on("data", (chunk) => {
          responseData += chunk;
        });
        res.on("end", () => {
          try {
            const statusCode = res.statusCode || 0;
            if (statusCode >= 400) {
              reject(new Error(`HTTP Error: ${statusCode} ${res.statusMessage}`));
              return;
            }
            let parsedData;
            const contentType = res.headers["content-type"] || "";
            if (contentType.includes("application/json") && responseData.trim()) {
              try {
                parsedData = JSON.parse(responseData);
              } catch (parseError) {
                parsedData = responseData;
              }
            } else {
              parsedData = responseData;
            }
            resolve({
              data: parsedData,
              status: statusCode,
              statusText: res.statusMessage || "OK",
              headers: res.headers
            });
          } catch (error) {
            reject(new Error(`Response parsing error: ${error instanceof Error ? error.message : "Unknown error"}`));
          }
        });
      });
      req.on("error", (error) => {
        reject(new Error(`Network error: ${error.message}`));
      });
      req.on("timeout", () => {
        req.destroy();
        reject(new Error(`Request timeout after ${requestOptions.timeout}ms`));
      });
      if (options.data && options.method !== "GET") {
        const postData = typeof options.data === "object" ? JSON.stringify(options.data) : String(options.data);
        req.write(postData);
      }
      req.end();
    } catch (error) {
      reject(new Error(`Request error: ${error instanceof Error ? error.message : "Unknown error"}`));
    }
  });
}
const FIGMA_TOKEN = "*********************************************";
async function figmaApiRequest({ url, params, headers }) {
  try {
    const response = await makeHttpRequest(url, {
      params,
      headers: {
        "X-FIGMA-TOKEN": FIGMA_TOKEN,
        ...headers
      }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
async function parseFigmaUrl(url) {
  try {
    const parsedUrl = new URL(url);
    if (!parsedUrl.hostname.includes("figma.com")) {
      throw new Error("不是有效的 Figma 链接");
    }
    const pathParts = parsedUrl.pathname.split("/").filter((part) => part.length > 0);
    if (pathParts.length < 2 || !["file", "design"].includes(pathParts[0])) {
      throw new Error("无效的 Figma 链接格式");
    }
    const fileKey = pathParts[1];
    let nodeId;
    const nodeIdParam = parsedUrl.searchParams.get("node-id");
    if (nodeIdParam) {
      nodeId = decodeURIComponent(nodeIdParam).replace(":", "-");
    }
    return {
      success: true,
      data: {
        fileKey,
        nodeId,
        originalUrl: url
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
function setupIpcHandlers() {
  electron.ipcMain.handle("figma-api-request", async (_event, { url, params, headers }) => {
    return await figmaApiRequest({ url, params, headers });
  });
  electron.ipcMain.handle("parse-figma-url", async (_event, url) => {
    return await parseFigmaUrl(url);
  });
}
node_module.createRequire(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("main.js", document.baseURI).href);
const __dirname$1 = path.dirname(node_url.fileURLToPath(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("main.js", document.baseURI).href));
process.env.DIST = path.join(__dirname$1, "../dist");
process.env.VITE_PUBLIC = electron.app.isPackaged ? process.env.DIST : path.join(process.env.DIST, "../public");
let win;
function createWindow() {
  win = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    icon: path.join(process.env.VITE_PUBLIC, "electron-vite.svg"),
    webPreferences: {
      preload: path.join(__dirname$1, "preload.js"),
      nodeIntegration: false,
      contextIsolation: true
    }
  });
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(process.env.VITE_DEV_SERVER_URL);
    win.webContents.openDevTools();
  } else {
    win.loadFile(path.join(process.env.DIST, "index.html"));
  }
}
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
    win = null;
  }
});
electron.app.on("activate", () => {
  if (electron.BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
electron.app.whenReady().then(() => {
  createWindow();
  setupIpcHandlers();
});
