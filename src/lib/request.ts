/**
 * 原生HTTP请求封装
 * 支持GET和POST请求，基于Node.js原生HTTP/HTTPS模块
 */

import * as http from 'http';
import * as https from 'https';

import * as querystring from 'querystring';

// 请求配置接口
export interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  timeout?: number;
  data?: any;
  params?: Record<string, any>;
}

// 响应接口
export interface RequestResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: http.IncomingHttpHeaders;
}

// 请求错误类
export class RequestError extends Error {
  public status?: number;
  public statusText?: string;
  public code?: string;

  constructor(message: string, status?: number, statusText?: string, code?: string) {
    super(message);
    this.name = 'RequestError';
    this.status = status;
    this.statusText = statusText;
    this.code = code;
  }
}

// 默认配置
const DEFAULT_CONFIG = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'Electron-App/1.0.0',
  },
};

/**
 * 将对象转换为URL查询参数
 */
function objectToQueryString(params: Record<string, any>): string {
  const filteredParams: Record<string, string> = {};

  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== null && value !== undefined) {
      filteredParams[key] = String(value);
    }
  });

  return querystring.stringify(filteredParams);
}

/**
 * 获取HTTP状态码对应的状态文本
 */
function getStatusText(statusCode: number): string {
  const statusTexts: Record<number, string> = {
    200: 'OK',
    201: 'Created',
    204: 'No Content',
    400: 'Bad Request',
    401: 'Unauthorized',
    403: 'Forbidden',
    404: 'Not Found',
    405: 'Method Not Allowed',
    500: 'Internal Server Error',
    502: 'Bad Gateway',
    503: 'Service Unavailable',
    504: 'Gateway Timeout',
  };

  return statusTexts[statusCode] || 'Unknown Status';
}

/**
 * 通用请求方法
 */
async function request<T = any>(config: RequestConfig): Promise<RequestResponse<T>> {
  const {
    url: requestUrl,
    method = 'GET',
    headers = {},
    timeout = DEFAULT_CONFIG.timeout,
    data,
    params,
  } = config;

  return new Promise((resolve, reject) => {
    try {
      // 解析URL
      const parsedUrl = new URL(requestUrl);

      // 添加查询参数
      if (params && Object.keys(params).length > 0) {
        const queryString = objectToQueryString(params);
        if (queryString) {
          parsedUrl.search = parsedUrl.search
            ? `${parsedUrl.search}&${queryString}`
            : `?${queryString}`;
        }
      }

      // 准备请求体
      let postData = '';
      if (data && method !== 'GET') {
        if (typeof data === 'object') {
          postData = JSON.stringify(data);
        } else {
          postData = String(data);
        }
      }

      // 构建请求选项
      const options: http.RequestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: method,
        headers: {
          ...DEFAULT_CONFIG.headers,
          ...headers,
        },
        timeout: timeout,
      };

      // 如果有请求体，设置Content-Length
      if (postData) {
        (options.headers as Record<string, string | number>)['Content-Length'] = Buffer.byteLength(postData);
      }

      // 选择HTTP或HTTPS模块
      const httpModule = parsedUrl.protocol === 'https:' ? https : http;

      // 创建请求
      const req = httpModule.request(options, (res) => {
        let responseData = '';

        // 收集响应数据
        res.on('data', (chunk) => {
          responseData += chunk;
        });

        // 响应结束
        res.on('end', () => {
          try {
            const statusCode = res.statusCode || 0;
            const statusText = getStatusText(statusCode);

            // 检查HTTP状态码
            if (statusCode >= 400) {
              reject(new RequestError(
                `HTTP Error: ${statusCode} ${statusText}`,
                statusCode,
                statusText
              ));
              return;
            }

            // 解析响应数据
            let parsedData: T;
            const contentType = res.headers['content-type'] || '';

            if (contentType.includes('application/json') && responseData.trim()) {
              try {
                parsedData = JSON.parse(responseData);
              } catch (parseError) {
                parsedData = responseData as unknown as T;
              }
            } else {
              parsedData = responseData as unknown as T;
            }

            resolve({
              data: parsedData,
              status: statusCode,
              statusText: statusText,
              headers: res.headers,
            });
          } catch (error) {
            reject(new RequestError(
              error instanceof Error ? error.message : 'Response parsing error',
              res.statusCode,
              getStatusText(res.statusCode || 0)
            ));
          }
        });
      });

      // 处理请求错误
      req.on('error', (error: NodeJS.ErrnoException) => {
        reject(new RequestError(
          error.message || 'Network error',
          0,
          'Network Error',
          error.code
        ));
      });

      // 处理超时
      req.on('timeout', () => {
        req.destroy();
        reject(new RequestError(
          `Request timeout after ${timeout}ms`,
          0,
          'Timeout'
        ));
      });

      // 发送请求体
      if (postData) {
        req.write(postData);
      }

      // 结束请求
      req.end();
    } catch (error) {
      reject(new RequestError(
        error instanceof Error ? error.message : 'Unknown error occurred',
        0,
        'Request Error'
      ));
    }
  });
}

/**
 * GET请求
 */
export function get<T = any>(
  url: string,
  params?: Record<string, any>,
  config?: Omit<RequestConfig, 'url' | 'method' | 'params'>
): Promise<RequestResponse<T>> {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config,
  });
}

/**
 * POST请求
 */
export function post<T = any>(
  url: string,
  data?: any,
  config?: Omit<RequestConfig, 'url' | 'method' | 'data'>
): Promise<RequestResponse<T>> {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config,
  });
}

// 导出默认实例
export default {
  request,
  get,
  post,
  RequestError,
};
