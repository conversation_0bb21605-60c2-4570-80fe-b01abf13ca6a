{"document": {"id": "46:6", "name": "Podcy - Home", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "76:411", "name": "Group 177", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "76:412", "name": "Rectangle 2", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.10000000149011612, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0, "b": 0.6000000238418579, "a": 1}}], "fillOverrideTable": {"1": null, "2": null, "3": null, "4": null}, "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "cornerRadius": 48, "cornerSmoothing": 0, "absoluteBoundingBox": {"x": 1239, "y": 846, "width": 364, "height": 72}, "absoluteRenderBounds": {"x": 1239, "y": 846, "width": 364, "height": 72}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "BACKGROUND_BLUR", "visible": true, "radius": 32}], "interactions": []}, {"id": "76:413", "name": "Ellipse 939", "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0, "b": 0.6000000238418579, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1293, "y": 903, "width": 5, "height": 5}, "absoluteRenderBounds": {"x": 1293, "y": 903, "width": 5, "height": 5}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}, {"id": "76:414", "name": "compass-fill", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "76:415", "name": "Rectangle 1704", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1363, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "76:416", "name": "Path 9734", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.12156862765550613, "g": 0.12156862765550613, "b": 0.12156862765550613, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1366, "y": 869, "width": 26.100006103515625, "height": 26.100008010864258}, "absoluteRenderBounds": {"x": 1366, "y": 869, "width": 26.0999755859375, "height": 26.10003662109375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "opacity": 0.5, "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1363, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": {"x": 1363, "y": 866, "width": 32, "height": 32}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "76:417", "name": "headphones-fill", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "76:418", "name": "Rectangle 1889", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1279, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "76:419", "name": "Path 9950", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.2980392277240753, "g": 0, "b": 0.6000000238418579, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1281.6666259765625, "y": 870.6666870117188, "width": 26.282052993774414, "height": 23.08832359313965}, "absoluteRenderBounds": {"x": 1281.6666259765625, "y": 870.6666870117188, "width": 26.2821044921875, "height": 23.08831787109375}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1279, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": {"x": 1279, "y": 866, "width": 32, "height": 32}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "76:420", "name": "heart-fill", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "76:421", "name": "Rectangle 1891", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1447, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "76:422", "name": "Path 9952", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.12156862765550613, "g": 0.12156862765550613, "b": 0.12156862765550613, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1449, "y": 870, "width": 27.10333251953125, "height": 24.090072631835938}, "absoluteRenderBounds": {"x": 1449, "y": 870, "width": 27.103271484375, "height": 24.090087890625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "opacity": 0.5, "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1447, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": {"x": 1447, "y": 866, "width": 32, "height": 32}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}, {"id": "76:423", "name": "Group 152", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "76:424", "name": "Rectangle 2987", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8509804010391235, "b": 0.8509804010391235, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1531, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "76:425", "name": "Union", "type": "BOOLEAN_OPERATION", "scrollBehavior": "SCROLLS", "children": [{"id": "76:426", "name": "Ellipse 937", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5916666388511658, "g": 0.5768749713897705, "b": 0.5768749713897705, "a": 1}}], "fillOverrideTable": {"2": null}, "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1535, "y": 884, "width": 24, "height": 10}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}, {"id": "76:427", "name": "Ellipse 938", "type": "ELLIPSE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.5916666388511658, "g": 0.5768749713897705, "b": 0.5768749713897705, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 1542, "y": 871, "width": 10, "height": 10}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "arcData": {"startingAngle": 0, "endingAngle": 6.2831854820251465, "innerRadius": 0}, "interactions": []}], "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.12156862765550613, "g": 0.12156862765550613, "b": 0.12156862765550613, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "booleanOperation": "UNION", "absoluteBoundingBox": {"x": 1535, "y": 871, "width": 24, "height": 23}, "absoluteRenderBounds": {"x": 1535, "y": 871, "width": 24, "height": 23}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "opacity": 0.5, "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1531, "y": 866, "width": 32, "height": 32}, "absoluteRenderBounds": {"x": 1531, "y": 866, "width": 32, "height": 32}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "rectangleCornerRadii": [0, 0, 0, 0], "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0, "g": 0, "b": 0, "a": 0}, "absoluteBoundingBox": {"x": 1239, "y": 846, "width": 364, "height": 72}, "absoluteRenderBounds": {"x": 1239, "y": 846, "width": 364, "height": 72}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1, "g": 1, "b": 1, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 1, "g": 1, "b": 1, "a": 1}, "absoluteBoundingBox": {"x": 1207, "y": 24, "width": 428, "height": 926}, "absoluteRenderBounds": {"x": 1207, "y": 24, "width": 428, "height": 926}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "exportSettings": [{"suffix": "", "format": "PNG", "constraint": {"type": "SCALE", "value": 2}}], "effects": [], "interactions": []}}