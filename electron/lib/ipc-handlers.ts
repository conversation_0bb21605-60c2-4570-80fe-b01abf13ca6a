import { ipcMain } from 'electron'
import { figmaApiRequest, parseFigmaUrl } from './figma-api'
import { processFigmaData } from '../../src/api/figma'

/**
 * 设置所有 IPC 处理器
 */
export function setupIpcHandlers(): void {
  // Figma API 请求处理器
  ipcMain.handle('figma-api-request', async (event, { url, params, headers }) => {
    const result = await figmaApiRequest({ url, params, headers })

    // 如果请求成功且是获取节点信息的请求，处理并打印数据
    if (result.success && result.data && url.includes('/nodes')) {
      console.log('📡 原始 Figma API 响应数据:')
      console.log(JSON.stringify(result.data, null, 2))
      console.log('')

      // 处理数据
      const processedData = processFigmaData(result.data)
      console.log('🔄 处理后的数据 (拆分子元素，YAML格式):')
      console.log(`共生成 ${processedData.length} 个YAML文档结构`)
      processedData.forEach((yamlDoc: string, index: number) => {
        console.log(`--- YAML文档 ${index + 1} ---`)
        console.log(yamlDoc)
        console.log('')
      })
    }

    return result
  })

  // Figma 链接解析处理器
  ipcMain.handle('parse-figma-url', async (event, url: string) => {
    return await parseFigmaUrl(url)
  })
}
