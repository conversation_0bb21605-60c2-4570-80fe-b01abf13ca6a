import * as https from 'https'
import * as http from 'http'
import * as querystring from 'querystring'

export interface HttpRequestOptions {
  method?: string
  params?: Record<string, any>
  headers?: Record<string, string>
  data?: any
  timeout?: number
}

export interface HttpResponse {
  data: any
  status: number
  statusText: string
  headers: http.IncomingHttpHeaders
}

/**
 * HTTP请求工具函数
 * 支持GET和POST请求，自动处理JSON响应
 */
export function makeHttpRequest(url: string, options: HttpRequestOptions = {}): Promise<HttpResponse> {
  return new Promise((resolve, reject) => {
    try {
      const parsedUrl = new URL(url)
      const httpModule = parsedUrl.protocol === 'https:' ? https : http

      // 添加查询参数
      if (options.params) {
        const queryString = querystring.stringify(options.params)
        if (queryString) {
          parsedUrl.search = parsedUrl.search
            ? `${parsedUrl.search}&${queryString}`
            : `?${queryString}`
        }
      }

      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Electron-App/1.0.0',
          ...options.headers
        },
        timeout: options.timeout || 10000
      }

      const req = httpModule.request(requestOptions, (res) => {
        let responseData = ''

        res.on('data', (chunk) => {
          responseData += chunk
        })

        res.on('end', () => {
          try {
            const statusCode = res.statusCode || 0

            if (statusCode >= 400) {
              reject(new Error(`HTTP Error: ${statusCode} ${res.statusMessage}`))
              return
            }

            let parsedData
            const contentType = res.headers['content-type'] || ''

            if (contentType.includes('application/json') && responseData.trim()) {
              try {
                parsedData = JSON.parse(responseData)
              } catch (parseError) {
                parsedData = responseData
              }
            } else {
              parsedData = responseData
            }

            resolve({
              data: parsedData,
              status: statusCode,
              statusText: res.statusMessage || 'OK',
              headers: res.headers
            })
          } catch (error) {
            reject(new Error(`Response parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`))
          }
        })
      })

      req.on('error', (error) => {
        reject(new Error(`Network error: ${error.message}`))
      })

      req.on('timeout', () => {
        req.destroy()
        reject(new Error(`Request timeout after ${requestOptions.timeout}ms`))
      })

      if (options.data && options.method !== 'GET') {
        const postData = typeof options.data === 'object' ? JSON.stringify(options.data) : String(options.data)
        req.write(postData)
      }

      req.end()
    } catch (error) {
      reject(new Error(`Request error: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  })
}
